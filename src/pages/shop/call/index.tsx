import { useState, useEffect } from "react";
import { ShopLayout } from "@/components/layout/ShopLayout";
import { SEO } from "@/components/SEO";
import { PageHeader } from "@/components/common/PageHeader";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  PhoneCall,
  Phone,
  PhoneOff,
  Mic,
  MicOff,
  Volume2,
  VolumeOff,
  Clock,
  User,
  Bot,
  FileText
} from "lucide-react";

interface CallHistory {
  id: string;
  type: 'ai' | 'agent';
  duration: string;
  time: string;
  status: 'completed' | 'missed' | 'busy';
  notes?: string;
  agentName?: string;
}

interface CallStatus {
  isConnected: boolean;
  isRinging: boolean;
  duration: number;
  isMuted: boolean;
  isSpeakerOn: boolean;
}

export default function ShopCall() {
  const [callStatus, setCallStatus] = useState<CallStatus>({
    isConnected: false,
    isRinging: false,
    duration: 0,
    isMuted: false,
    isSpeakerOn: false
  });
  const [callHistory, setCallHistory] = useState<CallHistory[]>([]);
  const [callNotes, setCallNotes] = useState("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCallHistory = async () => {
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        setCallHistory([
          {
            id: "1",
            type: "agent",
            duration: "5:32",
            time: "2 giờ trước",
            status: "completed",
            notes: "Đã giải quyết vấn đề về COD đơn hàng #DH001234",
            agentName: "Nguyễn Văn A"
          },
          {
            id: "2",
            type: "ai",
            duration: "2:15",
            time: "1 ngày trước",
            status: "completed",
            notes: "Hỗ trợ tự động về thông tin đơn hàng"
          },
          {
            id: "3",
            type: "agent",
            duration: "0:00",
            time: "2 ngày trước",
            status: "missed",
            agentName: "Trần Thị B"
          },
          {
            id: "4",
            type: "agent",
            duration: "8:45",
            time: "3 ngày trước",
            status: "completed",
            notes: "Hướng dẫn sử dụng hệ thống quản lý đơn hàng",
            agentName: "Lê Văn C"
          }
        ]);
      } catch (error) {
        console.error("Error fetching call history:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCallHistory();
  }, []);

  // Timer effect for call duration
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (callStatus.isConnected) {
      interval = setInterval(() => {
        setCallStatus(prev => ({
          ...prev,
          duration: prev.duration + 1
        }));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [callStatus.isConnected]);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleCallAI = () => {
    setCallStatus(prev => ({
      ...prev,
      isRinging: true
    }));

    // Simulate connection after 3 seconds
    setTimeout(() => {
      setCallStatus(prev => ({
        ...prev,
        isRinging: false,
        isConnected: true,
        duration: 0
      }));
    }, 3000);
  };

  const handleCallAgent = () => {
    setCallStatus(prev => ({
      ...prev,
      isRinging: true
    }));

    // Simulate connection after 5 seconds
    setTimeout(() => {
      setCallStatus(prev => ({
        ...prev,
        isRinging: false,
        isConnected: true,
        duration: 0
      }));
    }, 5000);
  };

  const handleEndCall = () => {
    setCallStatus({
      isConnected: false,
      isRinging: false,
      duration: 0,
      isMuted: false,
      isSpeakerOn: false
    });
  };

  const toggleMute = () => {
    setCallStatus(prev => ({
      ...prev,
      isMuted: !prev.isMuted
    }));
  };

  const toggleSpeaker = () => {
    setCallStatus(prev => ({
      ...prev,
      isSpeakerOn: !prev.isSpeakerOn
    }));
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Hoàn thành</Badge>;
      case 'missed':
        return <Badge variant="default" className="bg-red-100 text-red-800">Nhỡ cuộc gọi</Badge>;
      case 'busy':
        return <Badge variant="default" className="bg-yellow-100 text-yellow-800">Bận</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getCallTypeIcon = (type: string) => {
    return type === 'ai' ? <Bot className="h-4 w-4" /> : <User className="h-4 w-4" />;
  };

  return (
    <ShopLayout>
      <SEO title="Gọi tổng đài - Shop" description="Liên hệ trực tiếp với đội ngũ hỗ trợ" />
      <div className="space-y-6">
        <PageHeader
          title="Gọi tổng đài"
          description="Liên hệ trực tiếp với đội ngũ hỗ trợ qua điện thoại"
        />

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Call Interface */}
          <div className="space-y-6">
            {/* Call Status */}
            <Card>
              <CardHeader>
                <CardTitle>Trạng thái cuộc gọi</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                {callStatus.isRinging ? (
                  <div className="space-y-4">
                    <div className="w-20 h-20 mx-auto bg-yellow-100 rounded-full flex items-center justify-center animate-pulse">
                      <Phone className="h-8 w-8 text-yellow-600" />
                    </div>
                    <div>
                      <p className="font-medium">Đang kết nối...</p>
                      <p className="text-sm text-muted-foreground">Vui lòng chờ trong giây lát</p>
                    </div>
                  </div>
                ) : callStatus.isConnected ? (
                  <div className="space-y-4">
                    <div className="w-20 h-20 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                      <Phone className="h-8 w-8 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium">Đang trong cuộc gọi</p>
                      <p className="text-2xl font-mono">{formatDuration(callStatus.duration)}</p>
                    </div>
                    <div className="flex justify-center gap-4">
                      <Button
                        variant={callStatus.isMuted ? "default" : "outline"}
                        size="icon"
                        onClick={toggleMute}
                      >
                        {callStatus.isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant={callStatus.isSpeakerOn ? "default" : "outline"}
                        size="icon"
                        onClick={toggleSpeaker}
                      >
                        {callStatus.isSpeakerOn ? <Volume2 className="h-4 w-4" /> : <VolumeOff className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant="destructive"
                        size="icon"
                        onClick={handleEndCall}
                      >
                        <PhoneOff className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="w-20 h-20 mx-auto bg-muted rounded-full flex items-center justify-center">
                      <PhoneCall className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <div>
                      <p className="font-medium">Sẵn sàng gọi</p>
                      <p className="text-sm text-muted-foreground">Chọn loại hỗ trợ bên dưới</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Call Options */}
            {!callStatus.isConnected && !callStatus.isRinging && (
              <Card>
                <CardHeader>
                  <CardTitle>Tùy chọn cuộc gọi</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button
                    className="w-full h-auto p-4 flex items-center gap-4"
                    onClick={handleCallAI}
                  >
                    <div className="rounded-full bg-blue-100 p-3">
                      <Bot className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="text-left">
                      <div className="font-medium">Gọi AI Hỗ trợ</div>
                      <div className="text-sm text-muted-foreground">Hỗ trợ tự động 24/7</div>
                    </div>
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="w-full h-auto p-4 flex items-center gap-4"
                    onClick={handleCallAgent}
                  >
                    <div className="rounded-full bg-green-100 p-3">
                      <User className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="text-left">
                      <div className="font-medium">Gọi nhân viên CSKH</div>
                      <div className="text-sm text-muted-foreground">Hỗ trợ từ người thật</div>
                    </div>
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Call Notes */}
            {callStatus.isConnected && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Ghi chú cuộc gọi
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Ghi chú nội dung cuộc gọi..."
                    value={callNotes}
                    onChange={(e) => setCallNotes(e.target.value)}
                    rows={4}
                  />
                </CardContent>
              </Card>
            )}
          </div>

          {/* Call History */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Lịch sử cuộc gọi
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center text-muted-foreground py-8">
                  Đang tải...
                </div>
              ) : callHistory.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  Chưa có cuộc gọi nào
                </div>
              ) : (
                <div className="space-y-4">
                  {callHistory.map((call) => (
                    <div key={call.id} className="flex items-start gap-3 p-3 rounded-lg border">
                      <div className="rounded-full bg-muted p-2">
                        {getCallTypeIcon(call.type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div className="font-medium">
                            {call.type === 'ai' ? 'AI Hỗ trợ' : call.agentName || 'Nhân viên CSKH'}
                          </div>
                          {getStatusBadge(call.status)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Thời lượng: {call.duration} • {call.time}
                        </div>
                        {call.notes && (
                          <div className="text-sm mt-2 p-2 bg-muted rounded">
                            {call.notes}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </ShopLayout>
  );
}
