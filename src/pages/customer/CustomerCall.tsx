import { useState, useEffect } from "react";
import { CustomerLayout } from "@/components/layout/CustomerLayout";
import { SEO } from "@/components/SEO";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertTitle } from "@/components/ui/alert";
import { useAuth } from "@/contexts/AuthContext";
import { CallInterface } from "@/pages/call-center";
import { useSipCall } from "@/pages/call-center";
import { SIP_CONFIG } from "@/pages/call-center";
import {
  PhoneCall,
  Phone,
  PhoneOff,
  Mic,
  MicOff,
  Volume2,
  VolumeOff,
  Clock,
  User,
  Bot,
  FileText,
  PhoneIncoming,
  PhoneOutgoing,
  Info,
  Lock,
  Headphones
} from "lucide-react";

interface CallHistory {
  id: string;
  type: 'ai' | 'agent';
  duration: string;
  time: string;
  status: 'completed' | 'missed' | 'busy';
  notes?: string;
  agentName?: string;
}

export default function CustomerCall() {
  const { user } = useAuth();
  const [callHistory, setCallHistory] = useState<CallHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [username, setUsername] = useState(SIP_CONFIG.DEFAULT_USER);
  const [password, setPassword] = useState(SIP_CONFIG.DEFAULT_PASSWORD);

  const userRole = user?.role || 'shop';
  const userTitle = userRole === 'buyer' ? 'Người mua' : 'Cửa hàng';

  useEffect(() => {
    const fetchCallHistory = async () => {
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        setCallHistory([
          {
            id: "1",
            type: "agent",
            duration: "5:32",
            time: "2 giờ trước",
            status: "completed",
            notes: "Đã giải quyết vấn đề về COD đơn hàng #DH001234",
            agentName: "Nguyễn Văn A"
          },
          {
            id: "2",
            type: "ai",
            duration: "2:15",
            time: "1 ngày trước",
            status: "completed",
            notes: "Hỗ trợ tự động về thông tin đơn hàng"
          },
          {
            id: "3",
            type: "agent",
            duration: "0:00",
            time: "2 ngày trước",
            status: "missed",
            agentName: "Trần Thị B"
          }
        ]);
      } catch (error) {
        console.error("Error fetching call history:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCallHistory();
  }, []);

  const handleConnect = (e: React.FormEvent) => {
    e.preventDefault();
    setIsConnected(true);
  };



  const getCallTypeIcon = (type: 'ai' | 'agent') => {
    return type === 'ai' ? <Bot className="h-4 w-4" /> : <User className="h-4 w-4" />;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="secondary" className="text-green-600 bg-green-50">Hoàn thành</Badge>;
      case 'missed':
        return <Badge variant="secondary" className="text-red-600 bg-red-50">Nhỡ cuộc gọi</Badge>;
      case 'busy':
        return <Badge variant="secondary" className="text-yellow-600 bg-yellow-50">Bận</Badge>;
      default:
        return <Badge variant="secondary">Không xác định</Badge>;
    }
  };

  return (
    <CustomerLayout>
      <SEO
        title={`Gọi CSKH - ${userTitle}`}
        description="Gọi điện trực tiếp với nhân viên chăm sóc khách hàng"
      />

      <div className="space-y-6">
        {/* Page Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">
            Gọi nhân viên CSKH
          </h1>
          <p className="text-muted-foreground">
            Liên hệ trực tiếp để được hỗ trợ nhanh chóng qua WebRTC
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Call Interface */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Headphones className="h-5 w-5" />
                  Trung tâm cuộc gọi
                </CardTitle>
              </CardHeader>
              <CardContent>
                {!isConnected ? (
                  <form onSubmit={handleConnect} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="username" className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Tài khoản SIP
                      </Label>
                      <Input
                        id="username"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        required
                      />
                      <p className="text-sm text-muted-foreground">
                        Tài khoản mặc định: e1, e2, webrtc1000
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password" className="flex items-center gap-2">
                        <Lock className="h-4 w-4" />
                        Mật khẩu
                      </Label>
                      <Input
                        type="password"
                        id="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                      />
                      <p className="text-sm text-muted-foreground">
                        Mật khẩu mặc định: Xproz2025 (cho e1, e2), abc123 (cho webrtc1000)
                      </p>
                    </div>
                    <Button type="submit" className="w-full">
                      <PhoneCall className="mr-2 h-4 w-4" />
                      Kết nối
                    </Button>
                  </form>
                ) : (
                  <CallInterface username={username} password={password} />
                )}
              </CardContent>
            </Card>
          </div>

          {/* Instructions */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Hướng dẫn sử dụng
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium flex items-center gap-2 mb-2">
                    <PhoneCall className="h-4 w-4" />
                    Thực hiện cuộc gọi
                  </h3>
                  <ol className="list-decimal list-inside space-y-1 text-sm">
                    <li>Nhập tài khoản SIP và mật khẩu</li>
                    <li>Nhấn "Kết nối" để kết nối đến server</li>
                    <li>Nhập số điện thoại cần gọi</li>
                    <li>Nhấn "Gọi" để bắt đầu cuộc gọi</li>
                    <li>Sử dụng các nút điều khiển trong cuộc gọi</li>
                  </ol>
                </div>

                <div>
                  <h3 className="font-medium flex items-center gap-2 mb-2">
                    <PhoneIncoming className="h-4 w-4" />
                    Nhận cuộc gọi
                  </h3>
                  <ol className="list-decimal list-inside space-y-1 text-sm">
                    <li>Khi có cuộc gọi đến, nhấn "Trả lời"</li>
                    <li>Hoặc nhấn "Từ chối" để từ chối cuộc gọi</li>
                    <li>Sử dụng các nút điều khiển trong cuộc gọi</li>
                  </ol>
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>Lưu ý</AlertTitle>
                  <div className="text-sm mt-2">
                    <p>• Cần cho phép truy cập microphone</p>
                    <p>• Đảm bảo kết nối internet ổn định</p>
                    <p>• Sử dụng tai nghe để chất lượng tốt nhất</p>
                  </div>
                </Alert>

                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Số extension nội bộ:</h4>
                  <div className="text-sm space-y-1">
                    <div>• Extension 1: 1</div>
                    <div>• Extension 2: 2</div>
                    <div>• Demo: 6000</div>
                    <div>• Recording: 6001</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Call History */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Lịch sử cuộc gọi
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center text-muted-foreground py-8">
                Đang tải...
              </div>
            ) : callHistory.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                Chưa có cuộc gọi nào
              </div>
            ) : (
              <div className="space-y-4">
                {callHistory.map((call) => (
                  <div key={call.id} className="flex items-start gap-3 p-3 rounded-lg border">
                    <div className="rounded-full bg-muted p-2">
                      {getCallTypeIcon(call.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-medium">
                          {call.type === 'ai' ? 'Hỗ trợ tự động' : `CSKH - ${call.agentName}`}
                        </div>
                        {getStatusBadge(call.status)}
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <div>Thời lượng: {call.duration}</div>
                        <div>{call.time}</div>
                        {call.notes && (
                          <div className="text-foreground">{call.notes}</div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </CustomerLayout>
  );
}
