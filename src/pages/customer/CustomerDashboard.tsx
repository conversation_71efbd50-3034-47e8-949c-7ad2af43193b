import { CustomerLayout } from "@/components/layout/CustomerLayout";
import { SEO } from "@/components/SEO";
import { useAuth } from "@/contexts/AuthContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import {
  PhoneCall,
  MessageSquare,
  History,
  Clock,
  CheckCircle,
  AlertCircle,
  Package,
  Headphones,
  MessageCircle,
  FileText,
  ArrowRight
} from "lucide-react";

export default function CustomerDashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();

  const userRole = user?.role || 'shop';
  const basePath = userRole === 'buyer' ? '/buyer' : '/shop';
  const userTitle = userRole === 'buyer' ? 'Người mua' : '<PERSON><PERSON><PERSON> hàng';

  // Main service cards
  const mainServices = [
    {
      id: 'call',
      title: '<PERSON><PERSON><PERSON> nhân viên CSKH',
      description: '<PERSON><PERSON><PERSON> hệ trực tiếp với nhân viên hỗ trợ qua điện thoại',
      icon: <Headphones className="h-8 w-8" />,
      color: 'bg-blue-50 text-blue-600 border-blue-200',
      hoverColor: 'hover:bg-blue-100',
      path: `${basePath}/call`,
      features: ['Gọi ngay lập tức', 'Hỗ trợ 24/7', 'Ghi âm cuộc gọi']
    },
    {
      id: 'chat',
      title: 'Chat với CSKH',
      description: 'Trò chuyện trực tiếp với nhân viên hỗ trợ qua tin nhắn',
      icon: <MessageCircle className="h-8 w-8" />,
      color: 'bg-green-50 text-green-600 border-green-200',
      hoverColor: 'hover:bg-green-100',
      path: `${basePath}/support`,
      features: ['Chat real-time', 'Gửi file đính kèm', 'Lưu lịch sử']
    },
    {
      id: 'history',
      title: 'Lịch sử đơn hàng',
      description: 'Xem và theo dõi tất cả đơn hàng và yêu cầu hỗ trợ',
      icon: <FileText className="h-8 w-8" />,
      color: 'bg-purple-50 text-purple-600 border-purple-200',
      hoverColor: 'hover:bg-purple-100',
      path: `${basePath}/history`,
      features: ['Theo dõi đơn hàng', 'Lịch sử hỗ trợ', 'Tìm kiếm nhanh']
    }
  ];

  const quickActions = [
    {
      title: 'Gọi khẩn cấp',
      description: 'Hỗ trợ khẩn cấp 24/7',
      action: () => navigate(`${basePath}/call`),
      color: 'text-red-600'
    },
    {
      title: 'Chat nhanh',
      description: 'Bắt đầu chat ngay',
      action: () => navigate(`${basePath}/support`),
      color: 'text-blue-600'
    },
    {
      title: 'Tra cứu đơn hàng',
      description: 'Tìm kiếm đơn hàng',
      action: () => navigate(`${basePath}/history`),
      color: 'text-green-600'
    }
  ];

  return (
    <CustomerLayout>
      <SEO
        title={`Trang chính - ${userTitle}`}
        description="Trang chính dành cho khách hàng - Hỗ trợ CSKH"
      />

      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="text-center py-8">
          <h1 className="text-3xl font-bold text-foreground mb-4">
            Chào mừng bạn đến với CSKH!
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Chúng tôi luôn sẵn sàng hỗ trợ bạn 24/7. Hãy chọn cách thức liên hệ phù hợp nhất.
          </p>
        </div>

        {/* Main Services */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {mainServices.map((service) => (
            <Card
              key={service.id}
              className={`${service.color} border-2 ${service.hoverColor} transition-all duration-300 hover:shadow-lg cursor-pointer group`}
              onClick={() => navigate(service.path)}
            >
              <CardContent className="p-6">
                <div className="text-center space-y-4">
                  <div className="mx-auto w-16 h-16 rounded-full bg-white/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    {service.icon}
                  </div>

                  <div>
                    <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                    <p className="text-sm opacity-80 mb-4">{service.description}</p>
                  </div>

                  <div className="space-y-2">
                    {service.features.map((feature, index) => (
                      <div key={index} className="flex items-center justify-center gap-2 text-xs">
                        <CheckCircle className="h-3 w-3" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Button
                    variant="secondary"
                    className="w-full bg-white/20 hover:bg-white/30 border-0 group-hover:bg-white/40"
                  >
                    Bắt đầu
                    <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl text-center">Hỗ trợ nhanh</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto py-4 flex flex-col gap-2 hover:shadow-md transition-all"
                  onClick={action.action}
                >
                  <span className={`text-sm font-semibold ${action.color}`}>
                    {action.title}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {action.description}
                  </span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Help Section */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              Cần hỗ trợ thêm?
            </h3>
            <p className="text-blue-700 mb-4">
              Đội ngũ CSKH của chúng tôi luôn sẵn sàng giúp đỡ bạn mọi lúc, mọi nơi.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={() => navigate(`${basePath}/call`)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <PhoneCall className="h-4 w-4 mr-2" />
                Gọi ngay: 1900-xxxx
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate(`${basePath}/support`)}
                className="border-blue-300 text-blue-700 hover:bg-blue-50"
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Chat với chúng tôi
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </CustomerLayout>
  );
}
