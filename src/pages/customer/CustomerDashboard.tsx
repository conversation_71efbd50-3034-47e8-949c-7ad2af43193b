import { CustomerLayout } from "@/components/layout/CustomerLayout";
import { SEO } from "@/components/SEO";
import { useAuth } from "@/contexts/AuthContext";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import {
  PhoneCall,
  MessageSquare,
  History,
  ArrowRight,
  Headphones,
  MessageCircle,
  FileText
} from "lucide-react";

export default function CustomerDashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();

  const userRole = user?.role || 'shop';
  const basePath = userRole === 'buyer' ? '/buyer' : '/shop';
  const userTitle = userRole === 'buyer' ? 'Người mua' : 'Cửa hàng';

  // Main services - simplified
  const services = [
    {
      id: 'call',
      title: 'Gọi CSKH',
      description: '<PERSON>ê<PERSON> hệ trực tiếp qua điện thoại',
      icon: <Headphones className="h-12 w-12" />,
      gradient: 'from-blue-500 to-blue-600',
      path: `${basePath}/call`
    },
    {
      id: 'chat',
      title: 'Chat với CSKH',
      description: 'Trò chuyện qua tin nhắn',
      icon: <MessageCircle className="h-12 w-12" />,
      gradient: 'from-green-500 to-green-600',
      path: `${basePath}/support`
    },
    {
      id: 'history',
      title: 'Lịch sử đơn hàng',
      description: 'Xem đơn hàng và hỗ trợ',
      icon: <FileText className="h-12 w-12" />,
      gradient: 'from-purple-500 to-purple-600',
      path: `${basePath}/history`
    }
  ];

  return (
    <CustomerLayout>
      <SEO
        title={`Trang chính - ${userTitle}`}
        description="Trang chính dành cho khách hàng - Hỗ trợ CSKH"
      />

      <div className="min-h-[calc(100vh-200px)] flex flex-col">
        {/* Welcome Section */}
        <div className="text-center py-12">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Chào mừng đến với CSKH
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Chúng tôi luôn sẵn sàng hỗ trợ bạn 24/7. Chọn cách thức liên hệ phù hợp nhất.
          </p>
        </div>

        {/* Main Services */}
        <div className="flex-1 flex items-center justify-center">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 w-full max-w-6xl">
            {services.map((service) => (
              <Card
                key={service.id}
                className="group cursor-pointer border-0 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden"
                onClick={() => navigate(service.path)}
              >
                <div className={`h-2 bg-gradient-to-r ${service.gradient}`} />
                <CardContent className="p-8 text-center">
                  <div className={`mx-auto w-20 h-20 rounded-full bg-gradient-to-r ${service.gradient} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                    <div className="text-white">
                      {service.icon}
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold text-foreground mb-3">
                    {service.title}
                  </h3>

                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {service.description}
                  </p>

                  <Button
                    className={`w-full bg-gradient-to-r ${service.gradient} hover:opacity-90 text-white border-0 py-3 text-lg font-semibold group-hover:shadow-lg transition-all duration-300`}
                  >
                    Bắt đầu ngay
                    <ArrowRight className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Emergency Contact */}
        <div className="text-center py-8">
          <div className="inline-flex items-center gap-4 bg-red-50 border border-red-200 rounded-full px-6 py-3">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
            <span className="text-red-700 font-medium">
              Khẩn cấp? Gọi ngay:
              <Button
                variant="link"
                className="text-red-700 font-bold p-0 ml-1 text-lg"
                onClick={() => navigate(`${basePath}/call`)}
              >
                1900-xxxx
              </Button>
            </span>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
}
