import { useState } from "react";
import { CustomerLayout } from "@/components/layout/CustomerLayout";
import { SEO } from "@/components/SEO";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { 
  PhoneCall, 
  MessageSquare, 
  History, 
  Clock,
  CheckCircle,
  AlertCircle,
  Package,
  User,
  Settings
} from "lucide-react";

export default function CustomerDemo() {
  const navigate = useNavigate();
  const [userRole, setUserRole] = useState<'shop' | 'buyer'>('shop');

  const basePath = userRole === 'buyer' ? '/buyer' : '/shop';
  const userTitle = userRole === 'buyer' ? 'Người mua' : 'Cửa hàng';

  const quickStats = [
    {
      id: 'pending-orders',
      title: '<PERSON><PERSON>n hàng chờ xử lý',
      value: '3',
      description: '<PERSON><PERSON>n theo dõi',
      icon: <Clock className="h-5 w-5" />,
      color: 'text-yellow-600'
    },
    {
      id: 'completed-orders',
      title: 'Đơn hàng hoàn thành',
      value: '27',
      description: 'Tháng này',
      icon: <CheckCircle className="h-5 w-5" />,
      color: 'text-green-600'
    },
    {
      id: 'support-tickets',
      title: 'Yêu cầu hỗ trợ',
      value: '1',
      description: 'Đang xử lý',
      icon: <AlertCircle className="h-5 w-5" />,
      color: 'text-orange-600'
    }
  ];

  const recentActivities = [
    {
      id: '1',
      type: 'order',
      title: 'Đơn hàng #DH001234',
      description: 'Đã được giao thành công',
      time: '2 giờ trước',
      status: 'completed'
    },
    {
      id: '2',
      type: 'support',
      title: 'Yêu cầu hỗ trợ #SP001',
      description: 'Đang chờ phản hồi từ CSKH',
      time: '1 ngày trước',
      status: 'pending'
    },
    {
      id: '3',
      type: 'call',
      title: 'Cuộc gọi CSKH',
      description: 'Đã hoàn thành - 5 phút 32 giây',
      time: '2 ngày trước',
      status: 'completed'
    }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order':
        return <Package className="h-4 w-4" />;
      case 'support':
        return <MessageSquare className="h-4 w-4" />;
      case 'call':
        return <PhoneCall className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="secondary" className="text-green-600 bg-green-50">Hoàn thành</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="text-yellow-600 bg-yellow-50">Đang xử lý</Badge>;
      default:
        return <Badge variant="secondary">Không xác định</Badge>;
    }
  };

  return (
    <CustomerLayout>
      <SEO 
        title={`Demo Giao diện - ${userTitle}`} 
        description="Demo giao diện mới cho khách hàng" 
      />
      
      <div className="space-y-6">
        {/* Role Switcher */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Demo Controls
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Button
                variant={userRole === 'shop' ? 'default' : 'outline'}
                onClick={() => setUserRole('shop')}
              >
                <User className="h-4 w-4 mr-2" />
                Shop Role
              </Button>
              <Button
                variant={userRole === 'buyer' ? 'default' : 'outline'}
                onClick={() => setUserRole('buyer')}
              >
                <User className="h-4 w-4 mr-2" />
                Buyer Role
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Hiện tại đang xem với role: <strong>{userTitle}</strong>
            </p>
          </CardContent>
        </Card>

        {/* Welcome Section */}
        <div className="text-center py-8">
          <h1 className="text-3xl font-bold text-foreground mb-4">
            Demo Giao diện CSKH Mới
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Giao diện đã được nâng cấp với tính năng call center và chat real-time đầy đủ
          </p>
        </div>

        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-6 text-center">
              <PhoneCall className="h-12 w-12 mx-auto mb-4 text-blue-600" />
              <h3 className="text-lg font-semibold text-blue-900 mb-2">Call Center</h3>
              <p className="text-blue-700 text-sm">
                Tích hợp đầy đủ WebRTC SIP calling với tất cả tính năng từ call-center
              </p>
            </CardContent>
          </Card>

          <Card className="bg-green-50 border-green-200">
            <CardContent className="p-6 text-center">
              <MessageSquare className="h-12 w-12 mx-auto mb-4 text-green-600" />
              <h3 className="text-lg font-semibold text-green-900 mb-2">Real-time Chat</h3>
              <p className="text-green-700 text-sm">
                Sử dụng đầy đủ tính năng chat real-time từ chat-customers
              </p>
            </CardContent>
          </Card>

          <Card className="bg-purple-50 border-purple-200">
            <CardContent className="p-6 text-center">
              <History className="h-12 w-12 mx-auto mb-4 text-purple-600" />
              <h3 className="text-lg font-semibold text-purple-900 mb-2">Giao diện mới</h3>
              <p className="text-purple-700 text-sm">
                Trang chủ được thiết kế lại đơn giản, không có thống kê phức tạp
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Hoạt động gần đây</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg border">
                  <div className="rounded-full bg-muted p-2 mt-1">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium text-sm">{activity.title}</h4>
                      {getStatusBadge(activity.status)}
                    </div>
                    <p className="text-sm text-muted-foreground mb-1">
                      {activity.description}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Cần hỗ trợ?</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              <Button
                variant="outline"
                className="h-auto py-4 flex flex-col gap-2"
                onClick={() => navigate(`${basePath}/call`)}
              >
                <PhoneCall className="h-5 w-5 text-primary" />
                <span className="text-sm font-medium">Gọi ngay</span>
              </Button>
              
              <Button
                variant="outline"
                className="h-auto py-4 flex flex-col gap-2"
                onClick={() => navigate(`${basePath}/support`)}
              >
                <MessageSquare className="h-5 w-5 text-primary" />
                <span className="text-sm font-medium">Chat hỗ trợ</span>
              </Button>
              
              <Button
                variant="outline"
                className="h-auto py-4 flex flex-col gap-2"
                onClick={() => navigate(`${basePath}/history`)}
              >
                <History className="h-5 w-5 text-primary" />
                <span className="text-sm font-medium">Xem lịch sử</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Navigation Demo */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Test Navigation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate(`${basePath}/dashboard`)}
              >
                Dashboard
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate(`${basePath}/call`)}
              >
                Call Page
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate(`${basePath}/support`)}
              >
                Chat Page
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate(`${basePath}/history`)}
              >
                History Page
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </CustomerLayout>
  );
}
